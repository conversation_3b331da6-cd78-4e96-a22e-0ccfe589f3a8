# 前端接口修改说明

## 修改概述
将原有的场地信息接口修改为零工市场和用工信息接口，接口前缀改为 `http://localhost:80/sux-admin/`

## 主要修改内容

### 1. API基础配置修改
- **原接口前缀**: `/api`
- **新接口前缀**: `http://localhost:80/sux-admin`
- **API对象名**: `placeAPI` → `laborAPI`

### 2. 接口映射关系

#### 零工市场接口 (LaborMarketInfo)
- 获取零工市场列表: `/public/labor/markets`
- 获取推荐零工市场: `/public/labor/markets/featured`
- 获取活跃零工市场: `/public/labor/markets/active`
- 搜索零工市场: `/public/labor/markets/search`
- 获取零工市场统计: `/public/labor/statistics/markets`

#### 用工信息接口 (EmploymentInfo)
- 获取用工信息列表: `/public/labor/employments`
- 获取推荐用工信息: `/public/labor/employments/featured`
- 获取已发布用工信息: `/public/labor/employments/published`
- 搜索用工信息: `/public/labor/employments/search`

#### 综合接口
- 获取综合统计信息: `/public/labor/statistics/overview`
- 获取筛选选项: `/public/labor/filter-options`
- 快速匹配: `/public/labor/quick-match`

### 3. 实体字段映射

#### LaborMarketInfo 实体字段
- `marketId`: 市场ID
- `marketName`: 市场名称
- `marketType`: 市场类型（综合市场/专业市场/临时市场）
- `address`: 市场地址
- `regionCode`: 区域代码
- `regionName`: 区域名称
- `contactPerson`: 联系人
- `contactPhone`: 联系电话
- `serviceCategories`: 服务类别（JSON格式）
- `workerCapacity`: 零工容纳量
- `currentWorkerCount`: 当前零工数量
- `managementFee`: 管理费用
- `serviceFeeRate`: 服务费率
- `status`: 状态（"0"=正常，"1"=停用）
- `isFeatured`: 是否推荐（0=否，1=是）

#### EmploymentInfo 实体字段
- `employmentId`: 用工信息ID
- `title`: 用工标题
- `employmentType`: 用工类型（日结/周结/月结/计件）
- `workCategory`: 工作类别
- `workLocation`: 工作地点
- `regionCode`: 区域代码
- `regionName`: 区域名称
- `salaryType`: 薪资类型
- `salaryMin`: 最低薪资
- `salaryMax`: 最高薪资
- `workDescription`: 工作描述
- `contactPerson`: 联系人
- `contactPhone`: 联系电话
- `companyName`: 公司名称
- `positionsNeeded`: 需要人数
- `positionsFilled`: 已招聘人数
- `status`: 状态（"published"=已发布）
- `isFeatured`: 是否推荐（0=否，1=是）

### 4. 数据转换逻辑

#### 零工市场数据转换 (getCycdList函数)
```javascript
market.parkName = market.marketName;
market.parkAddress = market.address;
market.parkLevel = market.marketType;
market.parkType = '零工市场';
market.parkArea = market.workerCapacity ? market.workerCapacity + '人' : '待定';
market.usableArea = market.currentWorkerCount ? market.currentWorkerCount + '人' : '0人';
market.companyCount = market.currentWorkerCount || 0;
market.availablePositions = market.workerCapacity || 0;
market.occupiedPositions = market.currentWorkerCount || 0;
market.rentPriceMin = market.managementFee || 0;
market.rentPriceMax = market.serviceFeeRate || 0;
market.industryDirection = market.serviceCategories || '综合服务';
market.operationMode = market.operatingHours || '日常运营';
market.contactPerson = market.contactPerson;
market.contactPhone = market.contactPhone;
market.description = market.description;
market.imageUrl = market.imageUrl || './image/place_default.jpg';
market.baseId = market.marketId;
```

#### 用工信息数据转换 (getCdxqList和getQyzsList函数)
```javascript
employment.demandIntroduct = employment.workDescription || employment.description || '';
employment.demandTitle = employment.title || '招聘信息';
employment.demandType = employment.workCategory || employment.employmentType || '零工';
employment.salaryRange = employment.salaryMin && employment.salaryMax ? 
    employment.salaryMin + '-' + employment.salaryMax + '元/' + (employment.salaryType || '天') : 
    '面议';
employment.workLocation = employment.workLocation || employment.regionName || '';
employment.contactInfo = employment.contactPhone || employment.contactPerson || '';
employment.baseId = employment.employmentId;
employment.companyName = employment.companyName || '';
employment.positionsNeeded = employment.positionsNeeded || 0;
employment.positionsFilled = employment.positionsFilled || 0;
employment.urgencyLevel = employment.urgencyLevel || 'normal';
```

### 5. 筛选条件调整
- 区域筛选: 使用 `regionCode` 参数
- 市场类型筛选: 使用 `marketType` 参数
- 其他筛选条件暂时不适用于零工市场，可通过关键词搜索实现

### 6. 统计数据调整
- 零工市场总数: `totalMarkets`
- 工人容纳总量: `totalWorkerCapacity`
- 当前工人总数: `totalCurrentWorkers`
- 日均用工需求: `totalDailyDemand`

## 注意事项
1. 所有接口都是公开接口，无需登录验证
2. 零工市场状态过滤: `status = "0"` (正常状态)
3. 用工信息状态过滤: `status = "published"` (已发布状态)
4. 推荐标记: `isFeatured = 1`
5. 分页参数: `pageSize` 和 `pageNum`
6. 数据格式转换确保与现有前端模板兼容
